import { useState, useRef, useCallback, useEffect } from 'react';
import { message } from 'antd';
import chatService from '@/services/chat-service';
import { splitTextForTTS, AudioManager, AudioPlayState } from '@/utils/tts';

/**
 * TTS播放状态
 */
export interface TTSState {
  isPlaying: boolean;
  isPaused: boolean;
  isLoading: boolean;
  currentSentenceIndex: number;
  totalSentences: number;
  error?: string;
}

/**
 * TTS Hook
 */
export const useTTS = () => {
  const [state, setState] = useState<TTSState>({
    isPlaying: false,
    isPaused: false,
    isLoading: false,
    currentSentenceIndex: 0,
    totalSentences: 0,
  });

  const audioManagerRef = useRef<AudioManager | null>(null);
  const currentTextRef = useRef<string>('');
  const sentencesRef = useRef<string[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 初始化音频管理器
  useEffect(() => {
    audioManagerRef.current = new AudioManager(
      (audioState: AudioPlayState) => {
        setState(prev => ({
          ...prev,
          isLoading: audioState === AudioPlayState.LOADING,
          isPlaying: audioState === AudioPlayState.PLAYING,
          isPaused: audioState === AudioPlayState.PAUSED,
          error: audioState === AudioPlayState.ERROR ? '音频播放失败' : undefined,
        }));

        // 当前句子播放完成，播放下一句
        if (audioState === AudioPlayState.IDLE && sentencesRef.current.length > 0) {
          setTimeout(() => playNextSentence(), 0);
        }
      },
      (error: string) => {
        setState(prev => ({ ...prev, error, isLoading: false, isPlaying: false }));
        message.error(error);
      }
    );

    return () => {
      audioManagerRef.current?.destroy();
    };
  }, []);

  /**
   * 播放下一句
   */
  const playNextSentence = useCallback(async () => {
    const sentences = sentencesRef.current;

    setState(prevState => {
      const currentIndex = prevState.currentSentenceIndex;

      if (currentIndex >= sentences.length) {
        // 所有句子播放完成
        sentencesRef.current = [];
        return {
          ...prevState,
          isPlaying: false,
          currentSentenceIndex: 0,
          totalSentences: 0,
        };
      }

      const sentence = sentences[currentIndex];
      if (!sentence) return prevState;

      // 异步处理TTS API调用
      (async () => {
        try {
          setState(prev => ({ ...prev, isLoading: true }));

          // 创建新的AbortController
          abortControllerRef.current = new AbortController();

          // 调用TTS API
          const response = await fetch('/v1/conversation/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('token') || '',
            },
            body: JSON.stringify({ text: sentence }),
            signal: abortControllerRef.current.signal,
          });

          if (!response.ok) {
            throw new Error(`TTS API调用失败: ${response.status}`);
          }

          const audioBlob = await response.blob();

          // 播放音频
          await audioManagerRef.current?.playAudio(audioBlob);

          // 更新当前句子索引
          setState(prev => ({
            ...prev,
            currentSentenceIndex: currentIndex + 1,
            isLoading: false,
          }));

        } catch (error: any) {
          if (error.name === 'AbortError') {
            // 用户取消了播放
            return;
          }

          console.error('TTS播放失败:', error);
          setState(prev => ({
            ...prev,
            error: error.message || 'TTS播放失败',
            isLoading: false,
            isPlaying: false,
          }));
          message.error('语音合成失败');
        }
      })();

      return prevState;
    });
  }, []);

  /**
   * 开始TTS播放
   */
  const startTTS = useCallback(async (text: string) => {
    if (!text.trim()) {
      message.warning('没有可播放的文本');
      return;
    }

    // 停止当前播放
    stopTTS();

    // 分句处理
    const sentences = splitTextForTTS(text);
    if (sentences.length === 0) {
      message.warning('没有可播放的文本');
      return;
    }

    // 更新状态
    currentTextRef.current = text;
    sentencesRef.current = sentences;
    setState({
      isPlaying: true,
      isPaused: false,
      isLoading: false,
      currentSentenceIndex: 0,
      totalSentences: sentences.length,
      error: undefined,
    });

    // 开始播放第一句
    playNextSentence();
  }, [playNextSentence]);

  /**
   * 暂停TTS播放
   */
  const pauseTTS = useCallback(() => {
    audioManagerRef.current?.pause();
    setState(prev => ({ ...prev, isPaused: true, isPlaying: false }));
  }, []);

  /**
   * 恢复TTS播放
   */
  const resumeTTS = useCallback(() => {
    audioManagerRef.current?.resume();
    setState(prev => ({ ...prev, isPaused: false, isPlaying: true }));
  }, []);

  /**
   * 停止TTS播放
   */
  const stopTTS = useCallback(() => {
    // 取消网络请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // 停止音频播放
    audioManagerRef.current?.stop();

    // 重置状态
    setState({
      isPlaying: false,
      isPaused: false,
      isLoading: false,
      currentSentenceIndex: 0,
      totalSentences: 0,
      error: undefined,
    });

    // 清空引用
    currentTextRef.current = '';
    sentencesRef.current = [];
  }, []);

  /**
   * 切换播放/暂停
   */
  const toggleTTS = useCallback(() => {
    if (state.isPlaying) {
      pauseTTS();
    } else if (state.isPaused) {
      resumeTTS();
    }
  }, [state.isPlaying, state.isPaused, pauseTTS, resumeTTS]);

  return {
    state,
    startTTS,
    pauseTTS,
    resumeTTS,
    stopTTS,
    toggleTTS,
  };
};

/**
 * 全局TTS状态管理Hook
 * 确保同时只有一个TTS在播放
 */
let globalTTSInstance: ReturnType<typeof useTTS> | null = null;

export const useGlobalTTS = () => {
  const tts = useTTS();

  useEffect(() => {
    // 如果有其他TTS在播放，先停止
    if (globalTTSInstance && globalTTSInstance !== tts) {
      globalTTSInstance.stopTTS();
    }
    globalTTSInstance = tts;

    return () => {
      if (globalTTSInstance === tts) {
        globalTTSInstance = null;
      }
    };
  }, [tts]);

  return tts;
};
