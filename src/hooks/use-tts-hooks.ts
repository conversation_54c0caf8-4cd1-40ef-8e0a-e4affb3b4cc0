import { useState, useRef, useCallback, useEffect } from 'react';
import { message } from 'antd';
import chatService from '@/services/chat-service';
import { splitTextForTTS, AudioManager, AudioPlayState, testTTSTextProcessing, testBackendSplit } from '@/utils/tts';
import { getAuthorization } from '@/utils/authorization-util';
import { Authorization } from '@/constants/authorization';

// 在开发环境下添加全局测试函数
if (process.env.NODE_ENV === 'development') {
  (window as any).testTTS = testTTSTextProcessing;
  (window as any).testBackendSplit = testBackendSplit;
}

/**
 * TTS播放状态
 */
export interface TTSState {
  isPlaying: boolean;
  isPaused: boolean;
  isLoading: boolean;
  currentSentenceIndex: number;
  totalSentences: number;
  error?: string;
}

/**
 * TTS Hook
 */
export const useTTS = () => {
  const [state, setState] = useState<TTSState>({
    isPlaying: false,
    isPaused: false,
    isLoading: false,
    currentSentenceIndex: 0,
    totalSentences: 0,
  });

  const audioManagerRef = useRef<AudioManager | null>(null);
  const currentTextRef = useRef<string>('');
  const sentencesRef = useRef<string[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 初始化音频管理器
  useEffect(() => {
    audioManagerRef.current = new AudioManager(
      (audioState: AudioPlayState) => {
        setState(prev => ({
          ...prev,
          isLoading: audioState === AudioPlayState.LOADING,
          isPlaying: audioState === AudioPlayState.PLAYING,
          isPaused: audioState === AudioPlayState.PAUSED,
          error: audioState === AudioPlayState.ERROR ? '音频播放失败' : undefined,
        }));

        // 当前句子播放完成，播放下一句
        if (audioState === AudioPlayState.IDLE && sentencesRef.current.length > 0) {
          setTimeout(() => playNextSentence(), 0);
        }
      },
      (error: string) => {
        setState(prev => ({ ...prev, error, isLoading: false, isPlaying: false }));
        message.error(error);
      }
    );

    return () => {
      audioManagerRef.current?.destroy();
    };
  }, []);

  /**
   * 播放下一句
   */
  const playNextSentence = useCallback(async () => {
    const sentences = sentencesRef.current;

    setState(prevState => {
      const currentIndex = prevState.currentSentenceIndex;

      if (currentIndex >= sentences.length) {
        // 所有句子播放完成
        sentencesRef.current = [];
        return {
          ...prevState,
          isPlaying: false,
          currentSentenceIndex: 0,
          totalSentences: 0,
        };
      }

      const sentence = sentences[currentIndex];
      if (!sentence) return prevState;

      // 异步处理TTS API调用
      (async () => {
        try {
          setState(prev => ({ ...prev, isLoading: true }));

          // 验证句子内容
          if (!sentence || sentence.trim().length === 0) {
            console.warn('🎵 跳过空句子');
            setState(prev => ({
              ...prev,
              currentSentenceIndex: currentIndex + 1,
              isLoading: false,
            }));
            return;
          }

          // 模拟后端分割逻辑，确保不会产生空字符串
          const backendSplitTest = sentence.split(/[，。/《》？；：！\n\r:;]+/);
          const validParts = backendSplitTest.filter(part => part.trim().length > 0);
          if (validParts.length === 0) {
            console.warn('🎵 句子会被后端分割成空字符串，跳过:', sentence);
            setState(prev => ({
              ...prev,
              currentSentenceIndex: currentIndex + 1,
              isLoading: false,
            }));
            return;
          }

          console.log('🎵 开始TTS请求:', {
            sentence,
            length: sentence.length,
            trimmedLength: sentence.trim().length,
            authorization: getAuthorization() ? '已设置' : '未设置'
          });

          // 注意：按照原版ragflow的实现，我们不使用AbortController的signal
          // 这样可以避免请求被意外取消

          // 调用TTS API - 完全按照原版ragflow的方式
          const response = await fetch('/v1/conversation/tts', {
            method: 'POST',
            headers: {
              [Authorization]: getAuthorization(),
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: sentence }),
          });

          console.log('🎵 TTS响应状态:', {
            status: response.status,
            statusText: response.statusText,
            contentType: response.headers.get('content-type'),
            contentLength: response.headers.get('content-length')
          });

          // 先尝试检查是否有错误响应（参考原版ragflow实现）
          try {
            const res = await response.clone().json();
            if (res?.code !== 0) {
              message.error(res?.message || 'TTS请求失败');
              throw new Error(res?.message || 'TTS请求失败');
            }
          } catch (error: any) {
            // 如果JSON解析失败，说明返回的是音频流，这是正常的
            if (error.message && !error.message.includes('Unexpected token')) {
              throw error; // 重新抛出非JSON解析错误
            }
            console.log('🎵 TTS返回音频流（JSON解析失败是正常的）');
          }

          if (!response.ok) {
            throw new Error(`TTS API调用失败: ${response.status} ${response.statusText}`);
          }

          const audioBlob = await response.blob();

          console.log('🎵 音频数据:', {
            size: audioBlob.size,
            type: audioBlob.type
          });

          // 播放音频
          await audioManagerRef.current?.playAudio(audioBlob);

          // 更新当前句子索引
          setState(prev => ({
            ...prev,
            currentSentenceIndex: currentIndex + 1,
            isLoading: false,
          }));

        } catch (error: any) {
          console.error('🎵 TTS播放失败:', error);
          setState(prev => ({
            ...prev,
            error: error.message || 'TTS播放失败',
            isLoading: false,
            isPlaying: false,
          }));
          message.error('语音合成失败: ' + (error.message || '未知错误'));
        }
      })();

      return prevState;
    });
  }, []);

  /**
   * 开始TTS播放
   */
  const startTTS = useCallback(async (text: string) => {
    if (!text || typeof text !== 'string' || !text.trim()) {
      message.warning('没有可播放的文本');
      return;
    }

    console.log('🎵 开始TTS播放:', {
      originalText: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      originalLength: text.length
    });

    // 停止音频播放
    audioManagerRef.current?.stop();

    // 分句处理
    const sentences = splitTextForTTS(text);
    console.log('🎵 分句结果:', {
      sentenceCount: sentences.length,
      sentences: sentences.map(s => s.substring(0, 50) + (s.length > 50 ? '...' : ''))
    });

    if (sentences.length === 0) {
      message.warning('文本处理后没有可播放的内容');
      return;
    }

    // 更新状态
    currentTextRef.current = text;
    sentencesRef.current = sentences;
    setState({
      isPlaying: true,
      isPaused: false,
      isLoading: false,
      currentSentenceIndex: 0,
      totalSentences: sentences.length,
      error: undefined,
    });

    // 开始播放第一句
    setTimeout(() => playNextSentence(), 0);
  }, [playNextSentence]);

  /**
   * 暂停TTS播放
   */
  const pauseTTS = useCallback(() => {
    audioManagerRef.current?.pause();
    setState(prev => ({ ...prev, isPaused: true, isPlaying: false }));
  }, []);

  /**
   * 恢复TTS播放
   */
  const resumeTTS = useCallback(() => {
    audioManagerRef.current?.resume();
    setState(prev => ({ ...prev, isPaused: false, isPlaying: true }));
  }, []);

  /**
   * 停止TTS播放
   */
  const stopTTS = useCallback(() => {
    // 停止音频播放
    audioManagerRef.current?.stop();

    // 重置状态
    setState({
      isPlaying: false,
      isPaused: false,
      isLoading: false,
      currentSentenceIndex: 0,
      totalSentences: 0,
      error: undefined,
    });

    // 清空引用
    currentTextRef.current = '';
    sentencesRef.current = [];
  }, []);

  /**
   * 切换播放/暂停
   */
  const toggleTTS = useCallback(() => {
    if (state.isPlaying) {
      pauseTTS();
    } else if (state.isPaused) {
      resumeTTS();
    }
  }, [state.isPlaying, state.isPaused, pauseTTS, resumeTTS]);

  return {
    state,
    startTTS,
    pauseTTS,
    resumeTTS,
    stopTTS,
    toggleTTS,
  };
};

/**
 * 全局TTS状态管理Hook
 * 确保同时只有一个TTS在播放
 */
let globalTTSInstance: ReturnType<typeof useTTS> | null = null;

export const useGlobalTTS = () => {
  const tts = useTTS();

  useEffect(() => {
    // 如果有其他TTS在播放，先停止
    if (globalTTSInstance && globalTTSInstance !== tts) {
      globalTTSInstance.stopTTS();
    }
    globalTTSInstance = tts;

    return () => {
      if (globalTTSInstance === tts) {
        globalTTSInstance = null;
      }
    };
  }, [tts]);

  return tts;
};
