/**
 * TTS工具函数
 * 用于处理文本转语音功能的相关工具
 */

/**
 * 清洗文本，去除[ID:*]和换行符
 * @param text 原始文本
 * @returns 清洗后的文本
 */
export function cleanTextForTTS(text: string): string {
  if (!text) return '';
  
  // 去除[ID:*]格式的标识符
  let cleanedText = text.replace(/\[ID:[^\]]*\]/g, '');
  
  // 去除换行符
  cleanedText = cleanedText.replace(/\n/g, '');
  
  // 去除多余的空格
  cleanedText = cleanedText.replace(/\s+/g, ' ').trim();
  
  return cleanedText;
}

/**
 * 将文本分句，按照40字为界限
 * @param text 要分句的文本
 * @returns 分句后的文本数组
 */
export function splitTextForTTS(text: string): string[] {
  if (!text) return [];
  
  const cleanedText = cleanTextForTTS(text);
  
  // 如果文本长度小于等于40字，直接返回
  if (cleanedText.length <= 40) {
    return [cleanedText];
  }
  
  // 按照标点符号分句
  const sentences = cleanedText.split(/([。！？；：，])/);
  const result: string[] = [];
  let currentSentence = '';
  
  for (let i = 0; i < sentences.length; i++) {
    const part = sentences[i];
    
    // 如果是标点符号，直接添加到当前句子
    if (/[。！？；：，]/.test(part)) {
      currentSentence += part;
      
      // 检查当前句子长度
      if (currentSentence.length >= 40) {
        result.push(currentSentence.trim());
        currentSentence = '';
      }
    } else {
      // 如果添加这部分会超过40字，先保存当前句子
      if (currentSentence.length + part.length > 40 && currentSentence.length > 0) {
        result.push(currentSentence.trim());
        currentSentence = part;
      } else {
        currentSentence += part;
      }
    }
  }
  
  // 添加最后一个句子
  if (currentSentence.trim()) {
    result.push(currentSentence.trim());
  }
  
  // 如果没有分句成功，按照40字强制分割
  if (result.length === 0) {
    for (let i = 0; i < cleanedText.length; i += 40) {
      result.push(cleanedText.slice(i, i + 40));
    }
  }
  
  return result.filter(s => s.length > 0);
}

/**
 * 音频播放状态
 */
export enum AudioPlayState {
  IDLE = 'idle',
  LOADING = 'loading',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ERROR = 'error'
}

/**
 * 音频播放管理器
 */
export class AudioManager {
  private audio: HTMLAudioElement | null = null;
  private currentState: AudioPlayState = AudioPlayState.IDLE;
  private onStateChange?: (state: AudioPlayState) => void;
  private onError?: (error: string) => void;
  
  constructor(
    onStateChange?: (state: AudioPlayState) => void,
    onError?: (error: string) => void
  ) {
    this.onStateChange = onStateChange;
    this.onError = onError;
  }
  
  /**
   * 播放音频
   * @param audioBlob 音频数据
   */
  async playAudio(audioBlob: Blob): Promise<void> {
    try {
      this.setState(AudioPlayState.LOADING);
      
      // 停止当前播放的音频
      this.stop();
      
      // 创建新的音频对象
      const audioUrl = URL.createObjectURL(audioBlob);
      this.audio = new Audio(audioUrl);
      
      // 设置事件监听器
      this.audio.addEventListener('loadeddata', () => {
        this.setState(AudioPlayState.PLAYING);
      });
      
      this.audio.addEventListener('ended', () => {
        this.setState(AudioPlayState.IDLE);
        this.cleanup();
      });
      
      this.audio.addEventListener('error', (e) => {
        this.setState(AudioPlayState.ERROR);
        this.onError?.('音频播放失败');
        this.cleanup();
      });
      
      // 开始播放
      await this.audio.play();
      
    } catch (error) {
      this.setState(AudioPlayState.ERROR);
      this.onError?.('音频播放失败: ' + (error as Error).message);
      this.cleanup();
    }
  }
  
  /**
   * 暂停播放
   */
  pause(): void {
    if (this.audio && this.currentState === AudioPlayState.PLAYING) {
      this.audio.pause();
      this.setState(AudioPlayState.PAUSED);
    }
  }
  
  /**
   * 恢复播放
   */
  resume(): void {
    if (this.audio && this.currentState === AudioPlayState.PAUSED) {
      this.audio.play();
      this.setState(AudioPlayState.PLAYING);
    }
  }
  
  /**
   * 停止播放
   */
  stop(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.cleanup();
    }
    this.setState(AudioPlayState.IDLE);
  }
  
  /**
   * 获取当前状态
   */
  getState(): AudioPlayState {
    return this.currentState;
  }
  
  /**
   * 设置状态
   */
  private setState(state: AudioPlayState): void {
    this.currentState = state;
    this.onStateChange?.(state);
  }
  
  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.audio) {
      const audioUrl = this.audio.src;
      this.audio.removeEventListener('loadeddata', () => {});
      this.audio.removeEventListener('ended', () => {});
      this.audio.removeEventListener('error', () => {});
      this.audio = null;
      
      // 释放URL对象
      if (audioUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrl);
      }
    }
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stop();
    this.cleanup();
    this.onStateChange = undefined;
    this.onError = undefined;
  }
}
